Cursor AI 提升前端开发效率：Figma 集成与日常工作流优化
准备工作：连接 Cursor 与 Figma
在开始之前，请确保已完成 Cursor 与 Figma 的集成配置。

1.  进行 MCP 配置：在 Cursor 中配置 Figma MCP 服务。

    ○ Github 教程：https://github.com/GLips/Figma-Context-MCP/blob/main/README.zh.md

2.  获取 Figma Token：

        ○ 点击 Figma 右上角头像，进入 Settings。

        ○ 在 security -> generate new tokens 部分，创建一个新的 Token。

        ○ 为 Token 命名（如 "Cursor-mcp"），并将权限范围 (Scopes) 设置为 File content: Read and write。

        ○ 创建并复制生成的 Token，粘贴到 Cursor 的配置中。

    第一部分：结合 Figma MCP，改变 EDM 邮件开发流程
    核心目标： 将 Figma 设计稿自动化地转换为兼容性强、像素级精准的 HTML 邮件代码。
    第一步：设定精准的 Agent 规则 (Prompt)
    在存放 EDM 项目的文件夹中，为 Cursor 的 Agent 模式设定以下核心指令，以确保生成代码的规范性。

Prompt: EDM 开发专家指令
角色定位
你是一位顶尖的前端开发专家，专精于从 Figma 设计稿创建像素级精准、响应式的 HTML Email。你最重要的技能是 仅使用嵌套的 HTML <table> 结构来构建复杂布局，并将所有样式进行内联化处理。
核心任务
将提供的 Figma 设计稿转换为一个独立的、健壮的 HTML 文件，确保它能在所有主流邮件客户端（如 Outlook, Gmail, Apple Mail）中正确渲染。
严格规则与约束 (不可协商)

1. 只用表格进行布局:
   ● 必须 只使用 <table>, <tr>, <td> 标签来完成所有布局和结构性工作。
   ● 严禁 使用 <div>, <p>, <h1>-<h6> 或任何现代块级元素来构建布局。你可以在 <td> 内部使用 <p> 或 <span> 标签包裹文本，但不能用于创建间距或结构。
   ● 必须 使用嵌套表格（在一个 <td> 内嵌入另一个 <table>）来实现复杂布局、分栏和组件结构。
   ● 必须 充分利用 width, height, cellpadding="0", cellspacing="0", border="0", align, 和 valign 属性来控制布局。
2. 只用内联 CSS:
   ● 所有 CSS 样式 必须 是内联的，直接通过 style="..." 属性应用到 HTML 元素上。
   ● 禁止 在 <head> 或文档任何其他地方使用 <style> 标签。
   ● 禁止 使用 CSS class 或 ID。
   ● 禁止 链接任何外部 CSS 文件。
   细节与保真度要求
   ● 像素级复刻: 最终的 HTML 输出必须在视觉上与 Figma 设计稿完全一致。
   ● 间距与填充: 精确复制 Figma 文件中的所有间距、填充和边距。利用 cellpadding, cellspacing 以及 <td> 上的内联样式 style="padding: ...;" 来实现。
   ● 排版: 确保所有文本元素的字体、字号、字重、行高和颜色与 Figma 设计稿完全匹配，并将这些样式全部内联在 style 属性中。
   ● 图片: 所有图片都应从 Figma 导出，并使用完整的绝对 URL 引用。必须 为每个 <img> 标签设置 width, height, 和 alt 属性，并添加 style="display: block;" 以防止在某些邮件客户端中出现意外的间隙。
   执行指令
   现在，请严格遵循以上所有规则，将下面的 Figma 设计稿转换为一个独立的 HTML 邮件文件。[此处粘贴 FIGMA 设计稿链接]
   第二步：测试与评估
   我使用以下设计稿进行了测试：PE-BTS 原型图

● 方法一：直接链接转换
○ 流程: 直接将 Figma 设计稿链接提供给 Cursor，命令其自动下载图片并生成完整的 EDM 代码。
○ 结果:
■ 优点: Cursor 能够自动从 Figma 下载图片资源并完成基础的重命名。它在还原文案内容、字体样式和图片尺寸方面表现精确，优于 v0.dev 等工具。代码生成严格遵守了仅使用 <table> 标签的规则。
■ 缺点: 由于对 Figma 图层识别不够智能，自动下载的图片素材往往不符合最终需求。最终页面的视觉还原度有明显差距，样式混乱问题突出。

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDFelement - 返校季20%折扣优惠</title>
  </head>
  <body style="margin: 0; padding: 0; background-color: #ffffff; font-family: 'Messina Sans', 'HarmonyOS Sans', 'DM Sans', Arial, sans-serif">
    <!-- 主容器 -->
    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 600px; background-color: #ffffff">
      <!-- 头部Logo区域 -->
      <tr>
        <td style="padding: 16px 32px; background-color: #ffffff">
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td style="display: flex; align-items: center; gap: 5px">
                <!-- PDFelement Logo -->
                <table border="0" cellpadding="0" cellspacing="0">
                  <tr>
                    <td
                      style="
                        width: 32px;
                        height: 32px;
                        background: linear-gradient(135deg, #67a6ff 0%, #0061ff 100%);
                        border-radius: 7px;
                        text-align: center;
                        vertical-align: middle;
                      ">
                      <div style="width: 16px; height: 16px; background-color: #ffffff; margin: 8px auto; border-radius: 2px"></div>
                    </td>
                    <td style="padding-left: 8px">
                      <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td style="font-family: 'Messina Sans', Arial, sans-serif; font-size: 10px; font-weight: 400; color: #000000; line-height: 1em">
                            Wondershare
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              font-family: 'Messina Sans', Arial, sans-serif;
                              font-size: 16px;
                              font-weight: 700;
                              color: #000000;
                              line-height: 1em;
                              padding-top: 4px;
                            ">
                            PDFelement
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- 主标题横幅区域 -->
      <tr>
        <td style="padding: 0 16px">
          <table
            border="0"
            cellpadding="0"
            cellspacing="0"
            style="width: 568px; height: 481px; background: linear-gradient(180deg, #006dff 0%, #9ef4ff 100%); border-radius: 16px; position: relative">
            <tr>
              <td style="text-align: center; vertical-align: middle; padding: 31px 20px">
                <!-- 标题文字 -->
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
                  <tr>
                    <td
                      style="
                        font-family: 'Luckiest Guy', Arial, sans-serif;
                        font-size: 48px;
                        font-weight: 400;
                        color: #ffffff;
                        text-align: center;
                        line-height: 1em;
                        padding-bottom: 8px;
                      ">
                      Get Ready for
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        font-family: 'Luckiest Guy', Arial, sans-serif;
                        font-size: 68px;
                        font-weight: 400;
                        color: #ffffff;
                        text-align: center;
                        line-height: 1em;
                        padding-bottom: 4px;
                      ">
                      Back-to-School
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        font-family: 'Luckiest Guy', Arial, sans-serif;
                        font-size: 68px;
                        font-weight: 400;
                        color: #ffffff;
                        text-align: center;
                        line-height: 1em;
                        padding-bottom: 8px;
                      ">
                      with 20% off
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        font-family: 'Luckiest Guy', Arial, sans-serif;
                        font-size: 45px;
                        font-weight: 400;
                        color: #ffffff;
                        text-align: center;
                        line-height: 1em;
                      ">
                      on Your Next Purchase
                    </td>
                  </tr>
                </table>

                <!-- 工具图标装饰 -->
                <div style="position: absolute; right: 30px; bottom: 30px; width: 150px; height: 150px">
                  <!-- 这里可以添加工具图标的装饰元素 -->
                  <div
                    style="
                      width: 100%;
                      height: 100%;
                      background: rgba(255, 255, 255, 0.1);
                      border-radius: 50%;
                      border: 2px solid rgba(255, 255, 255, 0.3);
                    "></div>
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- 邮件正文内容 -->
      <tr>
        <td style="padding: 32px; color: #666666; font-family: 'DM Sans', Arial, sans-serif; font-size: 16px; line-height: 1.5em">
          Hey {=customer_name=},<br /><br />

          Thanks for being a valued {=wgp_redeem_sub_tpl_name=} member.<br />
          We noticed that your subscription expired on {=wgp_redeem_expire_date=}.<br /><br />

          We value your time and know it is precious. That's why we have dedicated months of development to improve our products.<br /><br />

          Now that the back-to-school season is here, please give us another chance - Come back to check out our significantly updated product and<br />
          <strong>Grab 20% OFF</strong> for Your Next Purchase today!
        </td>
      </tr>

      <!-- CTA按钮 -->
      <tr>
        <td style="text-align: center; padding: 24px 32px">
          <table border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto">
            <tr>
              <td style="background-color: #006dff; border-radius: 32px; padding: 19px 32px">
                <a
                  href="#"
                  style="
                    color: #ffffff;
                    text-decoration: none;
                    font-family: 'Messina Sans', Arial, sans-serif;
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 1.4em;
                  ">
                  Come Back With 20% OFF
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- 限时优惠提示 -->
      <tr>
        <td style="text-align: center; padding: 0 32px 24px; color: #666666; font-family: 'DM Sans', Arial, sans-serif; font-size: 12px; line-height: 1.67em">
          *Limited time offer: Expires in 14 days.
        </td>
      </tr>

      <!-- 产品特性展示区域 -->
      <tr>
        <td style="background-color: #eef5ff; padding: 48px 32px">
          <!-- 标题 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 24px">
            <tr>
              <td style="text-align: center">
                <h2
                  style="
                    font-family: 'HarmonyOS Sans', Arial, sans-serif;
                    font-size: 28px;
                    font-weight: 700;
                    color: #000000;
                    line-height: 1.29em;
                    margin: 0 0 12px 0;
                  ">
                  Excellent Features in PDFelement
                </h2>
                <p style="font-family: 'HarmonyOS Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; color: #000000; line-height: 1.5em; margin: 0">
                  From development to performance, we made no compromise at any level to make sure you get the best product.
                </p>
              </td>
            </tr>
          </table>

          <!-- 特性展示卡片 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <!-- 第一行：更快的文档渲染 + 云端文档管理 -->
              <td style="width: 50%; padding-right: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #ffffff; border-radius: 14px; margin-bottom: 24px">
                  <tr>
                    <td style="padding: 0">
                      <img
                        src="./images/feature_screenshot1.png"
                        alt="Document Rendering"
                        style="width: 100%; height: 160px; object-fit: cover; border-radius: 14px 14px 0 0; display: block" />
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 18px; text-align: center">
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 14px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.29em;
                          margin: 0;
                        ">
                        Faster Document Rendering
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="width: 50%; padding-left: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #ffffff; border-radius: 14px; margin-bottom: 24px">
                  <tr>
                    <td style="padding: 0">
                      <img
                        src="./images/feature_screenshot1.png"
                        alt="Cloud Management"
                        style="width: 100%; height: 160px; object-fit: cover; border-radius: 14px 14px 0 0; display: block" />
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 18px; text-align: center">
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 14px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.29em;
                          margin: 0;
                        ">
                        Document Management in Cloud
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
            <tr>
              <!-- 第二行：二维码文件传输 + AI多语言翻译 -->
              <td style="width: 50%; padding-right: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #ffffff; border-radius: 14px; margin-bottom: 24px">
                  <tr>
                    <td style="padding: 0">
                      <img
                        src="./images/feature_screenshot1.png"
                        alt="QR Code Transfer"
                        style="width: 100%; height: 160px; object-fit: cover; border-radius: 14px 14px 0 0; display: block" />
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 18px; text-align: center">
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 14px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.29em;
                          margin: 0;
                        ">
                        Transfer files between desktop and mobile using QR Code
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="width: 50%; padding-left: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #ffffff; border-radius: 14px; margin-bottom: 24px">
                  <tr>
                    <td style="padding: 0">
                      <img
                        src="./images/feature_screenshot1.png"
                        alt="AI Translation"
                        style="width: 100%; height: 160px; object-fit: cover; border-radius: 14px 14px 0 0; display: block" />
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 18px; text-align: center">
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 14px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.29em;
                          margin: 0;
                        ">
                        AI translate into multiple languages and grammar check
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- 第二个CTA按钮 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-top: 32px">
            <tr>
              <td style="text-align: center">
                <table border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto">
                  <tr>
                    <td style="background-color: #0061ff; border-radius: 64px; padding: 18px 64px">
                      <a
                        href="#"
                        style="
                          color: #ffffff;
                          text-decoration: none;
                          font-family: 'DM Sans', Arial, sans-serif;
                          font-size: 18px;
                          font-weight: 700;
                          line-height: 1.56em;
                        ">
                        Come Back With 20% OFF
                      </a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- 行业解决方案展示 -->
      <tr>
        <td style="padding: 48px 32px">
          <!-- 标题 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
            <tr>
              <td style="text-align: center">
                <h2 style="font-family: 'DM Sans', Arial, sans-serif; font-size: 28px; font-weight: 700; color: #000000; line-height: 1.14em; margin: 0">
                  PDF Industry Solutions
                </h2>
              </td>
            </tr>
          </table>

          <!-- 解决方案网格 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <!-- 第一行：教育 + 金融保险 -->
              <td style="width: 50%; padding-right: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
                  <tr>
                    <td style="text-align: center">
                      <div
                        style="
                          width: 261px;
                          height: 160px;
                          background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%);
                          border-radius: 8px;
                          margin-bottom: 12px;
                          position: relative;
                        ">
                        <img
                          src="./images/education_solution.png"
                          alt="Education Solution"
                          style="width: 261px; height: 150px; object-fit: cover; position: absolute; top: 10px; left: 0; display: block" />
                      </div>
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 16px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.5em;
                          margin: 0;
                        ">
                        Education
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="width: 50%; padding-left: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
                  <tr>
                    <td style="text-align: center">
                      <div
                        style="
                          width: 261px;
                          height: 160px;
                          background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%);
                          border-radius: 8px;
                          margin-bottom: 12px;
                          position: relative;
                        ">
                        <img
                          src="./images/finance_solution.png"
                          alt="Finance Solution"
                          style="width: 233px; height: 156px; object-fit: cover; position: absolute; top: 4px; left: 14px; display: block" />
                      </div>
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 16px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.5em;
                          margin: 0;
                        ">
                        Finance & Insurance
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
            <tr>
              <!-- 第二行：IT专业 + 法律政府 -->
              <td style="width: 50%; padding-right: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
                  <tr>
                    <td style="text-align: center">
                      <div
                        style="
                          width: 261px;
                          height: 160px;
                          background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%);
                          border-radius: 8px;
                          margin-bottom: 12px;
                          position: relative;
                        ">
                        <img
                          src="./images/it_solution.png"
                          alt="IT Solution"
                          style="width: 209px; height: 152px; object-fit: cover; position: absolute; top: 8px; left: 26px; display: block" />
                      </div>
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 16px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.5em;
                          margin: 0;
                        ">
                        IT& Professionals
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="width: 50%; padding-left: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
                  <tr>
                    <td style="text-align: center">
                      <div
                        style="
                          width: 261px;
                          height: 160px;
                          background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%);
                          border-radius: 8px;
                          margin-bottom: 12px;
                          position: relative;
                        ">
                        <img
                          src="./images/legal_solution.png"
                          alt="Legal Solution"
                          style="width: 203px; height: 152px; object-fit: cover; position: absolute; top: 8px; left: 29px; display: block" />
                      </div>
                      <h3
                        style="
                          font-family: 'HarmonyOS Sans', Arial, sans-serif;
                          font-size: 16px;
                          font-weight: 700;
                          color: #000000;
                          line-height: 1.5em;
                          margin: 0;
                        ">
                        Legal & Government
                      </h3>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- 了解更多按钮 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-top: 24px">
            <tr>
              <td style="text-align: center">
                <table border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto">
                  <tr>
                    <td style="background-color: #006dff; border-radius: 32px; padding: 12px 24px">
                      <a
                        href="#"
                        style="
                          color: #ffffff;
                          text-decoration: none;
                          font-family: 'Messina Sans', Arial, sans-serif;
                          font-size: 16px;
                          font-weight: 700;
                          line-height: 1.5em;
                        ">
                        Learn more
                      </a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- 支持信息 -->
      <tr>
        <td
          style="
            text-align: center;
            padding: 24px 32px;
            color: #666666;
            font-family: 'HarmonyOS Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.67em;
          ">
          If you have any questions or concerns, please feel free to contact our <a href="#" style="color: #006dff; text-decoration: none">Support Team</a>.
        </td>
      </tr>

      <!-- 结语 -->
      <tr>
        <td style="padding: 32px; color: #666666; font-family: 'DM Sans', Arial, sans-serif; font-size: 16px; line-height: 1em">
          Hoping to see you again and have an excellent day.<br /><br />
          PDFelement
        </td>
      </tr>

      <!-- 页脚 -->
      <tr>
        <td style="background-color: #000000; padding: 32px 32px 40px">
          <!-- 品牌和社交媒体 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 24px">
            <tr>
              <td style="vertical-align: top">
                <!-- Wondershare Logo -->
                <div style="width: 120px; height: 72px; margin-bottom: 16px">
                  <div style="color: #ffffff; font-family: Arial, sans-serif; font-size: 18px; font-weight: 700">Wondershare</div>
                </div>
              </td>
              <td style="text-align: right; vertical-align: top">
                <!-- 社交媒体 -->
                <div style="margin-bottom: 8px">
                  <span style="color: #999999; font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; line-height: 1.33em"> Connect with Us </span>
                </div>
                <div>
                  <a href="#" style="display: inline-block; width: 24px; height: 24px; margin-right: 12px; background-color: #999999; border-radius: 50%"></a>
                  <a href="#" style="display: inline-block; width: 24px; height: 24px; margin-right: 12px; background-color: #999999; border-radius: 50%"></a>
                  <a href="#" style="display: inline-block; width: 24px; height: 24px; margin-right: 12px; background-color: #999999; border-radius: 50%"></a>
                  <a href="#" style="display: inline-block; width: 24px; height: 24px; background-color: #999999; border-radius: 50%"></a>
                </div>
              </td>
            </tr>
          </table>

          <!-- 页脚文字 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td style="color: #999999; font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; line-height: 1.33em; margin-bottom: 8px">
                You are receiving this e-mail because you signed up to receive newsletters from Wondershare, If you do not want to receive this type of news in
                the future, please click to unsubscribe
              </td>
            </tr>
            <tr>
              <td
                style="color: #999999; font-family: 'Segoe UI', Arial, sans-serif; font-size: 12px; line-height: 1.33em; margin-bottom: 8px; padding-top: 8px">
                Please do not reply this letter directly, any questions, click to contact our support center.
              </td>
            </tr>
            <tr>
              <td style="color: #999999; font-family: 'Noto Sans', Arial, sans-serif; font-size: 12px; line-height: 1.33em; padding-top: 8px">
                <a href="#" style="color: #999999; text-decoration: none">About Wondershare</a> |
                <a href="#" style="color: #999999; text-decoration: none">Privacy Policy</a> |
                <a href="#" style="color: #999999; text-decoration: none">Support Team</a>
              </td>
            </tr>
            <tr>
              <td style="color: #999999; font-family: 'Noto Sans', Arial, sans-serif; font-size: 12px; line-height: 1.33em; padding-top: 8px">
                200-4445 Lougheed Hwy, Burnaby, BC Canada V5C 0E4
              </td>
            </tr>
            <tr>
              <td style="color: #999999; font-family: 'Noto Sans', Arial, sans-serif; font-size: 12px; line-height: 1.33em; padding-top: 8px">
                Copyright @ 2024 Wondershare Software Co. Ltd. All Rights Reserved.
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

  </body>
</html>

● 方法二：预处理图片 + 链接转换 (推荐)
○ 流程:
i. 先从 Figma 中手动导出所有需要的图片，并按需裁剪。
ii. 将图片存放在项目文件夹中。
iii. 向 Cursor 提供 Figma 链接，命令其分步执行：首先，根据设计稿的上下文和文案内容，为本地图片进行智能重命名；然后，基于这些已处理好的图片，参照预设规则生成 EDM 代码。
○ 结果:
■ 优点: 生成结果的页面还原度显著高于方法一。代码质量可靠，完全遵循预设规则。
■ 缺点: 仍可能存在少量布局 bug，需要人工审查和微调。
■ 结论: 相较于从零开始手写，此方法能够生成一个高质量的代码基础，将主要工作从“编写”转变为“审查与修正”，显著提升了开发效率。

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDFelement Back-to-School 20% OFF</title>
  </head>
  <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5">
    <!-- Main Container -->
    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 600px; background-color: #ffffff">
      <!-- Header -->
      <tr>
        <td align="center" style="padding: 16px 32px; background-color: #ffffff">
          <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td align="left" style="vertical-align: middle">
                <table border="0" cellpadding="0" cellspacing="0" style="width: auto">
                  <tr>
                    <td style="width: 32px; height: 32px; background-color: #0061ff; border-radius: 7px; text-align: center; vertical-align: middle">
                      <div style="width: 16px; height: 16px; background-color: #ffffff; margin: 8px auto; border-radius: 2px"></div>
                    </td>
                    <td style="padding-left: 8px; vertical-align: middle">
                      <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td style="font-family: Arial, sans-serif; font-size: 10px; font-weight: 400; color: #000000; line-height: 1.2">Wondershare</td>
                        </tr>
                        <tr>
                          <td style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 700; color: #000000; line-height: 1">PDFelement</td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- Hero Banner -->
      <tr>
        <td align="center" style="padding: 0">
          <img
            src="images_pdfelement_back_to_school/hero_banner.png"
            alt="Back-to-School Banner"
            width="568"
            height="481"
            style="display: block; border-radius: 16px; margin: 16px" />
        </td>
      </tr>

      <!-- Main Content -->
      <tr>
        <td style="padding: 32px">
          <!-- Welcome Text -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 400; color: #666666; line-height: 1.5; padding-bottom: 24px">
                Hey {=customer_name=},<br /><br />
                Thanks for being a valued {=wgp_redeem_sub_tpl_name=} member.<br />
                We noticed that your subscription expired on {=wgp_redeem_expire_date=}.<br /><br />
                We value your time and know it is precious. That's why we have dedicated months of development to improve our products.<br /><br />
                Now that the back-to-school season is here, please give us another chance - Come back to check out our significantly updated product and<br />
                <strong>Grab 20% OFF</strong> for Your Next Purchase today!
              </td>
            </tr>
          </table>

          <!-- Support Text -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td
                align="center"
                style="font-family: Arial, sans-serif; font-size: 12px; font-weight: 400; color: #666666; line-height: 1.67; padding-bottom: 24px">
                If you have any questions or concerns, please feel free to contact our Support Team.
              </td>
            </tr>
          </table>

          <!-- CTA Button -->
          <table align="center" border="0" cellpadding="0" cellspacing="0" style="margin-bottom: 24px">
            <tr>
              <td align="center" style="background-color: #006dff; border-radius: 32px; padding: 19px 32px">
                <a href="#" style="color: #ffffff; text-decoration: none; font-family: Arial, sans-serif; font-size: 18px; font-weight: 700; line-height: 1.44">
                  Come Back With 20% OFF
                </a>
              </td>
            </tr>
          </table>

          <!-- Expiry Notice -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td
                align="center"
                style="font-family: Arial, sans-serif; font-size: 12px; font-weight: 400; color: #666666; line-height: 1.67; padding-bottom: 48px">
                *Limited time offer: Expires in 14 days.
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- Features Section -->
      <tr>
        <td style="background-color: #eef5ff; padding: 48px 46px">
          <!-- Section Title -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 24px">
            <tr>
              <td align="center">
                <table border="0" cellpadding="0" cellspacing="0">
                  <tr>
                    <td
                      align="center"
                      style="font-family: Arial, sans-serif; font-size: 28px; font-weight: 700; color: #000000; line-height: 1.29; padding-bottom: 12px">
                      Excellent Features in PDFelement
                    </td>
                  </tr>
                  <tr>
                    <td
                      align="center"
                      style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 400; color: #000000; line-height: 1.5; width: 509px">
                      From development to performance, we made no compromise at any level to make sure you get the best product.
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Feature Cards -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 24px">
            <tr>
              <!-- Feature 1 -->
              <td style="width: 260px; padding-right: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #ffffff; border-radius: 14px">
                  <tr>
                    <td style="padding: 0">
                      <img
                        src="images_pdfelement_back_to_school/feature_screenshot1.png"
                        alt="Document Rendering"
                        width="260"
                        height="160"
                        style="display: block; border-radius: 14px 14px 0 0" />
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="padding: 18px 0; background-color: #ffffff; border-radius: 0 0 14px 14px">
                      <div style="font-family: Arial, sans-serif; font-size: 14px; font-weight: 700; color: #000000; line-height: 1.29">
                        Faster Document Rendering
                      </div>
                    </td>
                  </tr>
                </table>
              </td>

              <!-- Feature 2 -->
              <td style="width: 260px; padding-left: 16px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #ffffff; border-radius: 14px">
                  <tr>
                    <td style="padding: 0">
                      <img
                        src="images_pdfelement_back_to_school/feature_screenshot2.png"
                        alt="Cloud Management"
                        width="260"
                        height="160"
                        style="display: block; border-radius: 14px 14px 0 0" />
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="padding: 18px 0; background-color: #ffffff; border-radius: 0 0 14px 14px">
                      <div style="font-family: Arial, sans-serif; font-size: 14px; font-weight: 700; color: #000000; line-height: 1.29">
                        Document Management in Cloud
                      </div>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- CTA Button in Features Section -->
          <table align="center" border="0" cellpadding="0" cellspacing="0" style="margin-top: 24px">
            <tr>
              <td align="center" style="background-color: #0061ff; border-radius: 64px; padding: 18px 64px">
                <a href="#" style="color: #ffffff; text-decoration: none; font-family: Arial, sans-serif; font-size: 18px; font-weight: 700; line-height: 1.56">
                  Come Back With 20% OFF
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- Industry Solutions Section -->
      <tr>
        <td style="padding: 48px 32px">
          <!-- Section Title -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
            <tr>
              <td
                align="center"
                style="font-family: Arial, sans-serif; font-size: 28px; font-weight: 700; color: #000000; line-height: 1.14; padding-bottom: 24px">
                PDF Industry Solutions
              </td>
            </tr>
          </table>

          <!-- Solutions Row 1 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 20px">
            <tr>
              <!-- Education -->
              <td style="width: 261px; padding-right: 15px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
                  <tr>
                    <td style="background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%); border-radius: 8px; padding: 0">
                      <img
                        src="images_pdfelement_back_to_school/education_solution.png"
                        alt="Education Solution"
                        width="261"
                        height="160"
                        style="display: block; border-radius: 8px" />
                    </td>
                  </tr>
                  <tr>
                    <td
                      align="center"
                      style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 700; color: #000000; line-height: 1.5; padding-top: 12px">
                      Education
                    </td>
                  </tr>
                </table>
              </td>

              <!-- Finance & Insurance -->
              <td style="width: 261px; padding-left: 15px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
                  <tr>
                    <td style="background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%); border-radius: 8px; padding: 0">
                      <img
                        src="images_pdfelement_back_to_school/finance_solution.png"
                        alt="Finance Solution"
                        width="261"
                        height="160"
                        style="display: block; border-radius: 8px" />
                    </td>
                  </tr>
                  <tr>
                    <td
                      align="center"
                      style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 700; color: #000000; line-height: 1.5; padding-top: 12px">
                      Finance & Insurance
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Solutions Row 2 -->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; margin-bottom: 32px">
            <tr>
              <!-- IT & Professionals -->
              <td style="width: 261px; padding-right: 15px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
                  <tr>
                    <td style="background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%); border-radius: 8px; padding: 0">
                      <img
                        src="images_pdfelement_back_to_school/it_solution.png"
                        alt="IT Solution"
                        width="261"
                        height="160"
                        style="display: block; border-radius: 8px" />
                    </td>
                  </tr>
                  <tr>
                    <td
                      align="center"
                      style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 700; color: #000000; line-height: 1.5; padding-top: 12px">
                      IT& Professionals
                    </td>
                  </tr>
                </table>
              </td>

              <!-- Legal & Government -->
              <td style="width: 261px; padding-left: 15px; vertical-align: top">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
                  <tr>
                    <td style="background: linear-gradient(90deg, #d9e4ff 0%, #fffae4 100%); border-radius: 8px; padding: 0">
                      <img
                        src="images_pdfelement_back_to_school/legal_solution.png"
                        alt="Legal Solution"
                        width="261"
                        height="160"
                        style="display: block; border-radius: 8px" />
                    </td>
                  </tr>
                  <tr>
                    <td
                      align="center"
                      style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 700; color: #000000; line-height: 1.5; padding-top: 12px">
                      Legal & Government
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Learn More Button -->
          <table align="center" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <td align="center" style="background-color: #006dff; border-radius: 32px; padding: 12px 24px">
                <a href="#" style="color: #ffffff; text-decoration: none; font-family: Arial, sans-serif; font-size: 16px; font-weight: 700; line-height: 1.5">
                  Learn more
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- Closing Message -->
      <tr>
        <td style="padding: 32px">
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td style="font-family: Arial, sans-serif; font-size: 16px; font-weight: 400; color: #000000; line-height: 1">
                Hoping to see you again and have an excellent day.<br /><br />
                PDFelement
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <!-- Footer -->
      <tr>
        <td style="background-color: #000000; padding: 32px">
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tr>
              <td style="font-family: Arial, sans-serif; font-size: 12px; font-weight: 400; color: #999999; line-height: 1.33">
                You are receiving this e-mail because you signed up to receive newsletters from Wondershare, If you do not want to receive this type of news in
                the future, please click to unsubscribe<br /><br />
                Please do not reply this letter directly, any questions, click to contact our support center.<br /><br />
                About Wondershare | Privacy Policy | Support Team<br />
                200-4445 Lougheed Hwy, Burnaby, BC Canada V5C 0E4<br />
                Copyright @ 2024 Wondershare Software Co. Ltd. All Rights Reserved.
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

  </body>
</html>

    ○

总结:
在 EDM 编写场景下，通过“手动导出图片 + Cursor 辅助生成”的模式，其页面还原效果和开发效率均优于 v0.dev。推荐在未来的工作中采纳此方法以提高生产力。（测试时使用的是 Claude-4 模型，模型选择可能影响最终效果）。

第二部分：常规网页开发中的提效探索
核心目标： 探索在日常网页制作中，利用 Cursor 与 Figma MCP 服务提高效率的特定切入点。
第一步：设定网页开发规则 (Prompt)
在项目文件夹中，可以加入以下规则，规范页面生成标准。

Prompt: 网页开发专家指令
角色与核心理念
作为一名资深前端开发者，你擅长构建视觉丰富、交互性强的静态页面。核心开发方法论始终是 “桌面端优先” (Desktop-First)。
核心技术栈
● 布局与组件: 只使用 Bootstrap v4 进行响应式布局和基础组件构建。
● 轮播/滑块: 只使用 Swiper.js v7。
● 脚本: 强制使用 jQuery 作为所有脚本编写的主要库。这是关键规则。
CSS & 样式约定
● 所有 CSS 必须 采用“桌面端优先”的思路编写。从大屏幕的基础样式开始，然后使用 max-width 媒体查询来适配小屏幕。
● 积极使用更具表现力的 CSS 特性，包括复杂的 @keyframes 动画、平滑的 transition、transform 效果、渐变和精细的 box-shadow。
● 优先使用 Bootstrap v4 的功能类（如 .d-flex, .p-3），之后再编写新的自定义 CSS。
HTML Class 命名规范 (关键规则)
● 核心原则: 命名应反映元素的 “内容”或“功能”，而非其“样式”。（例如：正确 section-title，错误 big-red-text）。
● 命名格式: 单词间用 - 连接 (kebab-case)。（例如：product-card-image）。
● 组件化作用域: 使用组件名作为前缀来创建命名空间。（例如：product-card 组件内的 product-card-title）。
● 状态修饰符: 使用 is-_ (描述自身状态，如 is-active) 或 has-_ (描述包含关系，如 has-dropdown) 作为前缀。
输出与协作
● 最终交付一个独立的 .html 文件，所有 CSS 放在 <style> 标签内，所有 JS 放在 <script> 标签内。
● 使用中文（中文注释） 来解释复杂逻辑，特别是插件初始化或自定义动画部分。

第二步：测试与评估
● 直接生成整个页面：不推荐
○ 评估: 虽然 Cursor 能够完美复原设计稿中的所有文案，并且尝试使用 Bootstrap 栅格系统布局，但整体页面的样式还原度比 EDM 更差，布局效果远不理想。因此，不建议使用 MCP 服务直接生成完整的网页。
原设计稿：

还原出来的页面样式：

● 推荐的提效应用场景:
a. 批量图片智能重命名:
■ 流程: 在开发新页面前，先将所有图片从 Figma 切出并保存在一个文件夹内。然后，让 Cursor 读取整个设计稿，并根据图片在设计稿中的上下文（如所属模块、关联文案等）自动进行批量重命名。
■ 优势: 相比于截图识别工具，接入 MCP 能让 AI 更精准地理解图片用途，生成语义化的文件名，省去大量手动重命名的时间。
b. 页面文案的精准填充:
■ 流程: 当开发具有相似布局的页面时，可以先快速搭建好 HTML 结构，文案部分用占位符代替。随后，接入 Figma 设计稿 MCP，命令 Cursor 基于现有页面结构，自动抓取并填充所有对应的文案。
■ 优势: 能确保文案与设计稿 100% 一致，杜绝了手动复制粘贴可能产生的遗漏或错误，在文案内容繁多的页面中尤其高效。

第三部分：Cursor 在日常编码中的通用提效技巧 (非 MCP)
除了与 Figma 的联动，Cursor 编辑器本身在日常开发中也提供了多个强大的提效功能：

1. 上下文感知代码补全 (Tab 键提示):
   这是 Cursor 最核心的优势之一。它能深入分析项目或目录中已有的代码风格、命名规范和逻辑模式，并提供极其精准的自动补全建议，准确率远超同类 AI 编辑器。
2. 代码重构与文档生成:
   在完成初步编码后，可以选中代码块，让 Cursor 对逻辑进行重构优化，使其更简洁、高效。同时，可以命令它为复杂的函数或模块生成详尽的中文注释，极大地提高了代码的可读性和后续的可维护性。
3. 业务逻辑快速实现:
   对于新的业务需求（例如：“当用户点击弹窗的关闭按钮时，有 30% 的几率触发文件下载”），可以直接用自然语言向 Cursor 描述需求。如果描述足够清晰，它能以极高的准确率生成所需的功能代码片段。
4. 自动化语义化类名编写:
   在编写完页面结构并填充内容后，可以选中整个 HTML 结构，让 Cursor 自动为所有元素赋予符合语义化和项目规范的类名。这不仅省去了思考命名的精力，还能确保整个项目类名风格的一致性和专业性。
